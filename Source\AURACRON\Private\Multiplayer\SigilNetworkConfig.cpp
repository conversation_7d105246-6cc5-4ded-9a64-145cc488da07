// Copyright Epic Games, Inc. All Rights Reserved.
// AURACRON - Sistema de Sígilos MOBA 5x5
// Arquivo: SigilNetworkConfig.cpp
// Descrição: Implementação robusta das configurações de rede otimizadas para UE 5.6
// Inclui: Predição de cliente, Rollback, Anti-cheat, EOS, Telemetria, Métricas

#include "Multiplayer/SigilNetworkConfig.h"
#include "GameplayTagsManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/NetDriver.h"
#include "Engine/NetConnection.h"
#include "UObject/CoreNet.h"
#include "UObject/CoreNetTypes.h"
#include "UObject/CoreNetContext.h"
#include "Net/UnrealNetwork.h"
#include "Engine/NetworkSubsystem.h"
#include "Engine/EngineReplicationBridge.h"
#include "Engine/NetworkMetricsConfig.h"
#include "Engine/PhysicsReplication.h"
#include "Engine/NetPushModelHelpers.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionInterface.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "NetworkPrediction/Public/NetworkPredictionComponent.h"
#include "TimerManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/DateTime.h"
#include "Logging/LogMacros.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CsvProfiler.h"

// Definir categoria de log para o sistema de rede AURACRON
DEFINE_LOG_CATEGORY_STATIC(LogAURACRONNetwork, Log, All);

// Definir stats para profiling
DECLARE_STATS_GROUP(TEXT("AURACRON Network"), STATGROUP_AURACRONNetwork, STATCAT_Advanced);
DECLARE_CYCLE_STAT(TEXT("Network Optimization"), STAT_NetworkOptimization, STATGROUP_AURACRONNetwork);
DECLARE_CYCLE_STAT(TEXT("Interest Management"), STAT_InterestManagement, STATGROUP_AURACRONNetwork);
DECLARE_CYCLE_STAT(TEXT("Anti-Cheat Validation"), STAT_AntiCheatValidation, STATGROUP_AURACRONNetwork);
DECLARE_DWORD_COUNTER_STAT(TEXT("Replications Per Second"), STAT_ReplicationsPerSecond, STATGROUP_AURACRONNetwork);
DECLARE_FLOAT_COUNTER_STAT(TEXT("Bandwidth Usage (KB/s)"), STAT_BandwidthUsage, STATGROUP_AURACRONNetwork);

// Definir tags de gameplay para networking AURACRON
namespace AURACRONNetworkTags
{
    // Tags de Sígilos para replicação
    const FGameplayTag Sigil_Aegis_Network = FGameplayTag::RequestGameplayTag(FName("Network.Sigil.Aegis"));
    const FGameplayTag Sigil_Ruin_Network = FGameplayTag::RequestGameplayTag(FName("Network.Sigil.Ruin"));
    const FGameplayTag Sigil_Vesper_Network = FGameplayTag::RequestGameplayTag(FName("Network.Sigil.Vesper"));

    // Tags de Ambientes para replicação
    const FGameplayTag Environment_Planicie = FGameplayTag::RequestGameplayTag(FName("Network.Environment.Planicie"));
    const FGameplayTag Environment_Firmamento = FGameplayTag::RequestGameplayTag(FName("Network.Environment.Firmamento"));
    const FGameplayTag Environment_Reino = FGameplayTag::RequestGameplayTag(FName("Network.Environment.Reino"));

    // Tags de Trilhos para replicação
    const FGameplayTag Trilho_Solar = FGameplayTag::RequestGameplayTag(FName("Network.Trilho.Solar"));
    const FGameplayTag Trilho_Lunar = FGameplayTag::RequestGameplayTag(FName("Network.Trilho.Lunar"));
    const FGameplayTag Trilho_Axis = FGameplayTag::RequestGameplayTag(FName("Network.Trilho.Axis"));

    // Tags de Objetivos para replicação
    const FGameplayTag Objetivo_Torre = FGameplayTag::RequestGameplayTag(FName("Network.Objetivo.Torre"));
    const FGameplayTag Objetivo_Guardiao = FGameplayTag::RequestGameplayTag(FName("Network.Objetivo.Guardiao"));
    const FGameplayTag Objetivo_Nucleo = FGameplayTag::RequestGameplayTag(FName("Network.Objetivo.Nucleo"));

    // Tags de Anti-Cheat
    const FGameplayTag AntiCheat_SpeedHack = FGameplayTag::RequestGameplayTag(FName("Network.AntiCheat.SpeedHack"));
    const FGameplayTag AntiCheat_Teleport = FGameplayTag::RequestGameplayTag(FName("Network.AntiCheat.Teleport"));
    const FGameplayTag AntiCheat_InvalidAction = FGameplayTag::RequestGameplayTag(FName("Network.AntiCheat.InvalidAction"));
}

// Constantes
const float USigilNetworkConfig::MIN_REPLICATION_FREQUENCY = 1.0f;
const float USigilNetworkConfig::MAX_REPLICATION_FREQUENCY = 120.0f;
const float USigilNetworkConfig::MIN_DISTANCE = 100.0f;
const float USigilNetworkConfig::MAX_DISTANCE = 50000.0f;
const int32 USigilNetworkConfig::MIN_PLAYERS = 2;
const int32 USigilNetworkConfig::MAX_PLAYERS = 20;

USigilNetworkConfig::USigilNetworkConfig()
{
    // Configurações padrão para MOBA 5x5 AURACRON
    OptimizationType = ESigilNetworkOptimization::MOBA;
    MaxPlayers = 10;
    bEnableNetworkOptimizations = true;
    bEnableDebugLogging = false;

    // Configurações MOBA específicas
    bPrioritizeTeammates = true;
    bReduceEnemyDetails = true;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 2000.0f;

    // Inicializar configurações avançadas do UE 5.6
    bEnableNetworkPrediction = true;
    bEnableRollbackNetworking = true;
    bEnableAdvancedDeltaCompression = true;
    bEnableInterestManagement = true;
    bEnableAntiCheatValidation = true;
    bEnableEOSIntegration = true;
    bEnableNetworkTelemetry = true;
    bEnableLagCompensation = true;
    bEnableNetworkMetrics = true;

    // Configurações de predição de cliente
    ClientPredictionSettings.bEnableMovementPrediction = true;
    ClientPredictionSettings.bEnableAbilityPrediction = true;
    ClientPredictionSettings.MaxPredictionTime = 0.5f;
    ClientPredictionSettings.PredictionTolerance = 0.1f;

    // Configurações de rollback
    RollbackSettings.MaxRollbackFrames = 60;
    RollbackSettings.RollbackTolerance = 0.05f;
    RollbackSettings.bEnableRollbackLogging = false;

    // Configurações de interest management
    InterestManagementSettings.MaxRelevantActors = 200;
    InterestManagementSettings.RelevanceUpdateFrequency = 10.0f;
    InterestManagementSettings.SpatialHashGridSize = 1000.0f;
    InterestManagementSettings.bUseDistanceCulling = true;
    InterestManagementSettings.bUseFrustumCulling = true;

    // Configurações de anti-cheat
    AntiCheatSettings.bValidateMovement = true;
    AntiCheatSettings.bValidateAbilities = true;
    AntiCheatSettings.bValidateResources = true;
    AntiCheatSettings.MaxMovementSpeedTolerance = 1.2f;
    AntiCheatSettings.ValidationFrequency = 20.0f;
    AntiCheatSettings.SuspiciousActionThreshold = 5;

    // Configurações de lag compensation
    LagCompensationSettings.MaxCompensationTime = 0.2f;
    LagCompensationSettings.bCompensateMovement = true;
    LagCompensationSettings.bCompensateHitscan = true;
    LagCompensationSettings.bCompensateProjectiles = true;

    // Configurações de telemetria
    TelemetrySettings.bCollectBandwidthStats = true;
    TelemetrySettings.bCollectLatencyStats = true;
    TelemetrySettings.bCollectPacketLossStats = true;
    TelemetrySettings.bCollectReplicationStats = true;
    TelemetrySettings.TelemetryUpdateFrequency = 5.0f;

    // Configurações específicas do AURACRON
    AURACRONNetworkSettings.bReplicateSigilStates = true;
    AURACRONNetworkSettings.bReplicateEnvironmentTransitions = true;
    AURACRONNetworkSettings.bReplicateTrilhoEffects = true;
    AURACRONNetworkSettings.bReplicateFluxoPrismal = true;
    AURACRONNetworkSettings.SigilReplicationPriority = 8.0f;
    AURACRONNetworkSettings.EnvironmentTransitionRadius = 3000.0f;
    AURACRONNetworkSettings.TrilhoEffectRadius = 1500.0f;
    AURACRONNetworkSettings.FluxoPrismalUpdateFrequency = 30.0f;

    // Aplicar otimizações MOBA por padrão
    ApplyMOBAOptimizations();

    // Configurar tags de prioridade específicas do AURACRON
    ConfigureAURACRONNetworkTags();

    // Inicializar sistema de métricas
    InitializeNetworkMetrics();

    // Inicializar timers para sistemas automáticos
    InitializeNetworkTimers();

    // Log de inicialização
    UE_LOG(LogAURACRONNetwork, Log, TEXT("SigilNetworkConfig inicializado com configurações robustas UE 5.6"));
}

void USigilNetworkConfig::ApplyOptimizationType(ESigilNetworkOptimization NewType)
{
    OptimizationType = NewType;
    
    switch (NewType)
    {
        case ESigilNetworkOptimization::None:
            ApplyNoOptimizations();
            break;
            
        case ESigilNetworkOptimization::Basic:
            ApplyBasicOptimizations();
            break;
            
        case ESigilNetworkOptimization::MOBA:
            ApplyMOBAOptimizations();
            break;
            
        case ESigilNetworkOptimization::Competitive:
            ApplyCompetitiveOptimizations();
            break;
            
        case ESigilNetworkOptimization::Custom:
            // Manter configurações atuais
            break;
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Applied optimization type %d"), (int32)NewType);
}

void USigilNetworkConfig::SetMOBAOptimizations(bool bEnable)
{
    bPrioritizeTeammates = bEnable;
    bReduceEnemyDetails = bEnable;
    bOptimizeForObjectives = bEnable;
    
    if (bEnable)
    {
        ApplyMOBAOptimizations();
    }
    else
    {
        ApplyBasicOptimizations();
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: MOBA optimizations %s"), 
        bEnable ? TEXT("enabled") : TEXT("disabled"));
}

void USigilNetworkConfig::SetMaxPlayers(int32 NewMaxPlayers)
{
    MaxPlayers = FMath::Clamp(NewMaxPlayers, MIN_PLAYERS, MAX_PLAYERS);
    
    // Ajustar configurações baseadas no número de jogadores
    if (MaxPlayers <= 4)
    {
        // Configurações para jogos pequenos
        FrequencySettings.BaseReplicationFrequency = 30.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 100.0f;
    }
    else if (MaxPlayers <= 10)
    {
        // Configurações para MOBA 5x5
        FrequencySettings.BaseReplicationFrequency = 20.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 50.0f;
    }
    else
    {
        // Configurações para jogos grandes
        FrequencySettings.BaseReplicationFrequency = 15.0f;
        BandwidthSettings.MaxBandwidthPerPlayer = 30.0f;
    }
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Max players set to %d"), MaxPlayers);
}

void USigilNetworkConfig::ResetToDefaults()
{
    // Resetar para configurações MOBA padrão
    OptimizationType = ESigilNetworkOptimization::MOBA;
    MaxPlayers = 10;
    bEnableNetworkOptimizations = true;
    bEnableDebugLogging = false;
    
    ApplyMOBAOptimizations();
    
    UE_LOG(LogTemp, Log, TEXT("SigilNetworkConfig: Reset to default MOBA settings"));
}

float USigilNetworkConfig::GetPriorityForTag(FGameplayTag Tag) const
{
    if (CriticalTags.HasTag(Tag))
    {
        return 10.0f; // Prioridade máxima
    }
    else if (HighPriorityTags.HasTag(Tag))
    {
        return 7.0f; // Prioridade alta
    }
    else if (LowPriorityTags.HasTag(Tag))
    {
        return 2.0f; // Prioridade baixa
    }
    
    return 5.0f; // Prioridade padrão
}

float USigilNetworkConfig::GetDistanceMultiplier(float Distance) const
{
    if (Distance <= DistanceSettings.LowPriorityDistance)
    {
        return 1.0f; // Multiplicador completo
    }
    else if (Distance <= DistanceSettings.MediumPriorityDistance)
    {
        return 0.7f; // Multiplicador médio
    }
    else if (Distance <= DistanceSettings.MaxReplicationDistance)
    {
        return 0.4f; // Multiplicador baixo
    }
    else if (Distance <= DistanceSettings.CullingDistance)
    {
        return 0.1f; // Multiplicador mínimo
    }
    
    return 0.0f; // Sem replicação
}

float USigilNetworkConfig::GetFrequencyForPriority(float Priority) const
{
    if (Priority >= 8.0f)
    {
        return FrequencySettings.CriticalDataFrequency;
    }
    else if (Priority >= 5.0f)
    {
        return FrequencySettings.BaseReplicationFrequency;
    }
    else
    {
        return FrequencySettings.LowPriorityFrequency;
    }
}

bool USigilNetworkConfig::ShouldReplicateAtDistance(float Distance) const
{
    return Distance <= DistanceSettings.CullingDistance;
}

bool USigilNetworkConfig::ValidateConfiguration() const
{
    return ValidatePriorities() && ValidateDistances() && ValidateFrequencies() && ValidateBandwidth();
}

TArray<FString> USigilNetworkConfig::GetConfigurationWarnings() const
{
    TArray<FString> Warnings;
    
    if (!ValidatePriorities())
    {
        Warnings.Add(TEXT("Invalid priority settings detected"));
    }
    
    if (!ValidateDistances())
    {
        Warnings.Add(TEXT("Invalid distance settings detected"));
    }
    
    if (!ValidateFrequencies())
    {
        Warnings.Add(TEXT("Invalid frequency settings detected"));
    }
    
    if (!ValidateBandwidth())
    {
        Warnings.Add(TEXT("Invalid bandwidth settings detected"));
    }
    
    if (MaxPlayers > 10 && OptimizationType == ESigilNetworkOptimization::MOBA)
    {
        Warnings.Add(TEXT("MOBA optimization is designed for 10 players or less"));
    }
    
    if (BandwidthSettings.MaxBandwidthPerPlayer * MaxPlayers > 1000.0f)
    {
        Warnings.Add(TEXT("Total bandwidth usage may be too high"));
    }
    
    return Warnings;
}

float USigilNetworkConfig::GetEstimatedBandwidthUsage() const
{
    // Estimativa baseada em configurações atuais
    float BaseBandwidth = BandwidthSettings.MaxBandwidthPerPlayer * MaxPlayers;
    
    // Aplicar multiplicadores baseados em otimizações
    switch (OptimizationType)
    {
        case ESigilNetworkOptimization::None:
            return BaseBandwidth;
            
        case ESigilNetworkOptimization::Basic:
            return BaseBandwidth * 0.8f;
            
        case ESigilNetworkOptimization::MOBA:
            return BaseBandwidth * 0.6f;
            
        case ESigilNetworkOptimization::Competitive:
            return BaseBandwidth * 0.5f;
            
        case ESigilNetworkOptimization::Custom:
            return BaseBandwidth * 0.7f;
    }
    
    return BaseBandwidth;
}

int32 USigilNetworkConfig::GetEstimatedReplicationsPerSecond() const
{
    // Estimativa baseada em frequência e número de jogadores
    float BaseReplications = FrequencySettings.BaseReplicationFrequency * MaxPlayers;
    
    // Considerar diferentes tipos de dados
    float CriticalReplications = FrequencySettings.CriticalDataFrequency * MaxPlayers * 0.1f; // 10% críticos
    float LowPriorityReplications = FrequencySettings.LowPriorityFrequency * MaxPlayers * 0.3f; // 30% baixa prioridade
    
    return FMath::RoundToInt(BaseReplications + CriticalReplications + LowPriorityReplications);
}

USigilNetworkConfig* USigilNetworkConfig::GetSigilNetworkConfig()
{
    return GetMutableDefault<USigilNetworkConfig>();
}

// Configurações predefinidas
void USigilNetworkConfig::ApplyBasicOptimizations()
{
    // Configurações básicas de otimização
    ReplicationPriorities.EquippedSigilPriority = 5.0f;
    ReplicationPriorities.FusionPriority = 7.0f;
    ReplicationPriorities.SystemStatsPriority = 3.0f;
    ReplicationPriorities.VFXPriority = 2.0f;
    ReplicationPriorities.TeammatePriority = 5.0f;
    ReplicationPriorities.EnemyPriority = 5.0f;
    
    DistanceSettings.MaxReplicationDistance = 6000.0f;
    DistanceSettings.MediumPriorityDistance = 4000.0f;
    DistanceSettings.LowPriorityDistance = 2000.0f;
    DistanceSettings.CullingDistance = 12000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 20.0f;
    FrequencySettings.CriticalDataFrequency = 40.0f;
    FrequencySettings.LowPriorityFrequency = 10.0f;
    FrequencySettings.OptimizationInterval = 2.0f;
    
    BandwidthSettings.MaxBandwidthPerPlayer = 60.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 40;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = false;
}

void USigilNetworkConfig::ApplyMOBAOptimizations()
{
    // Configurações otimizadas para MOBA 5x5
    ReplicationPriorities.EquippedSigilPriority = 6.0f;
    ReplicationPriorities.FusionPriority = 8.0f;
    ReplicationPriorities.SystemStatsPriority = 3.0f;
    ReplicationPriorities.VFXPriority = 2.0f;
    ReplicationPriorities.TeammatePriority = 7.0f; // Priorizar companheiros de equipe
    ReplicationPriorities.EnemyPriority = 4.0f; // Reduzir detalhes de inimigos
    
    DistanceSettings.MaxReplicationDistance = 5000.0f; // Distância típica de MOBA
    DistanceSettings.MediumPriorityDistance = 3000.0f;
    DistanceSettings.LowPriorityDistance = 1500.0f;
    DistanceSettings.CullingDistance = 10000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 20.0f;
    FrequencySettings.CriticalDataFrequency = 60.0f; // Alta frequência para dados críticos
    FrequencySettings.LowPriorityFrequency = 5.0f;
    FrequencySettings.OptimizationInterval = 1.0f; // Otimização mais frequente
    
    BandwidthSettings.MaxBandwidthPerPlayer = 50.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 50;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = true; // Usar delta compression
    
    // Configurações específicas para MOBA
    bPrioritizeTeammates = true;
    bReduceEnemyDetails = true;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 2000.0f;
}

void USigilNetworkConfig::ApplyCompetitiveOptimizations()
{
    // Configurações para modo competitivo (máxima precisão)
    ReplicationPriorities.EquippedSigilPriority = 8.0f;
    ReplicationPriorities.FusionPriority = 9.0f;
    ReplicationPriorities.SystemStatsPriority = 6.0f;
    ReplicationPriorities.VFXPriority = 3.0f;
    ReplicationPriorities.TeammatePriority = 8.0f;
    ReplicationPriorities.EnemyPriority = 7.0f; // Manter detalhes de inimigos
    
    DistanceSettings.MaxReplicationDistance = 7000.0f; // Maior alcance
    DistanceSettings.MediumPriorityDistance = 5000.0f;
    DistanceSettings.LowPriorityDistance = 2500.0f;
    DistanceSettings.CullingDistance = 15000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 30.0f; // Maior frequência
    FrequencySettings.CriticalDataFrequency = 120.0f; // Frequência máxima
    FrequencySettings.LowPriorityFrequency = 10.0f;
    FrequencySettings.OptimizationInterval = 0.5f; // Otimização muito frequente
    
    BandwidthSettings.MaxBandwidthPerPlayer = 100.0f; // Maior bandwidth
    BandwidthSettings.MaxReplicationsPerFrame = 100;
    BandwidthSettings.bUseCompression = true;
    BandwidthSettings.bUseDeltaCompression = true;
    
    // Configurações competitivas
    bPrioritizeTeammates = false; // Tratar todos igualmente
    bReduceEnemyDetails = false;
    bOptimizeForObjectives = true;
    ObjectiveReplicationRadius = 3000.0f; // Maior raio para objetivos
}

void USigilNetworkConfig::ApplyNoOptimizations()
{
    // Sem otimizações (máxima qualidade, máximo uso de rede)
    ReplicationPriorities.EquippedSigilPriority = 10.0f;
    ReplicationPriorities.FusionPriority = 10.0f;
    ReplicationPriorities.SystemStatsPriority = 10.0f;
    ReplicationPriorities.VFXPriority = 10.0f;
    ReplicationPriorities.TeammatePriority = 10.0f;
    ReplicationPriorities.EnemyPriority = 10.0f;
    
    DistanceSettings.MaxReplicationDistance = 20000.0f;
    DistanceSettings.MediumPriorityDistance = 15000.0f;
    DistanceSettings.LowPriorityDistance = 10000.0f;
    DistanceSettings.CullingDistance = 50000.0f;
    
    FrequencySettings.BaseReplicationFrequency = 60.0f;
    FrequencySettings.CriticalDataFrequency = 120.0f;
    FrequencySettings.LowPriorityFrequency = 30.0f;
    FrequencySettings.OptimizationInterval = 5.0f;
    
    BandwidthSettings.MaxBandwidthPerPlayer = 200.0f;
    BandwidthSettings.MaxReplicationsPerFrame = 200;
    BandwidthSettings.bUseCompression = false;
    BandwidthSettings.bUseDeltaCompression = false;
    
    bPrioritizeTeammates = false;
    bReduceEnemyDetails = false;
    bOptimizeForObjectives = false;
}

// Validação interna
bool USigilNetworkConfig::ValidatePriorities() const
{
    return ReplicationPriorities.EquippedSigilPriority >= 0.0f && ReplicationPriorities.EquippedSigilPriority <= 10.0f &&
           ReplicationPriorities.FusionPriority >= 0.0f && ReplicationPriorities.FusionPriority <= 10.0f &&
           ReplicationPriorities.SystemStatsPriority >= 0.0f && ReplicationPriorities.SystemStatsPriority <= 10.0f &&
           ReplicationPriorities.VFXPriority >= 0.0f && ReplicationPriorities.VFXPriority <= 10.0f &&
           ReplicationPriorities.TeammatePriority >= 0.0f && ReplicationPriorities.TeammatePriority <= 10.0f &&
           ReplicationPriorities.EnemyPriority >= 0.0f && ReplicationPriorities.EnemyPriority <= 10.0f;
}

bool USigilNetworkConfig::ValidateDistances() const
{
    return DistanceSettings.LowPriorityDistance >= MIN_DISTANCE &&
           DistanceSettings.MediumPriorityDistance >= DistanceSettings.LowPriorityDistance &&
           DistanceSettings.MaxReplicationDistance >= DistanceSettings.MediumPriorityDistance &&
           DistanceSettings.CullingDistance >= DistanceSettings.MaxReplicationDistance &&
           DistanceSettings.CullingDistance <= MAX_DISTANCE;
}

bool USigilNetworkConfig::ValidateFrequencies() const
{
    return FrequencySettings.LowPriorityFrequency >= MIN_REPLICATION_FREQUENCY &&
           FrequencySettings.BaseReplicationFrequency >= FrequencySettings.LowPriorityFrequency &&
           FrequencySettings.CriticalDataFrequency >= FrequencySettings.BaseReplicationFrequency &&
           FrequencySettings.CriticalDataFrequency <= MAX_REPLICATION_FREQUENCY &&
           FrequencySettings.OptimizationInterval >= 0.1f && FrequencySettings.OptimizationInterval <= 10.0f;
}

bool USigilNetworkConfig::ValidateBandwidth() const
{
    return BandwidthSettings.MaxBandwidthPerPlayer >= 1.0f && BandwidthSettings.MaxBandwidthPerPlayer <= 1000.0f &&
           BandwidthSettings.MaxReplicationsPerFrame >= 1 && BandwidthSettings.MaxReplicationsPerFrame <= 200 &&
           BandwidthSettings.MaxPacketSize >= 512 && BandwidthSettings.MaxPacketSize <= 8192;
}

// Implementações das novas funções robustas para UE 5.6

void USigilNetworkConfig::ConfigureAURACRONNetworkTags()
{
    // Configurar tags de alta prioridade específicas do AURACRON
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Fusion.Active")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Fusion.Active")));
    }

    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Equipped")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Equipped")));
    }

    // Tags críticas para MOBA 5x5
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("MOBA.TeamFight")))
    {
        CriticalTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("MOBA.TeamFight")));
    }

    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("MOBA.Objective.Critical")))
    {
        CriticalTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("MOBA.Objective.Critical")));
    }

    // Tags específicas dos 3 Sígilos AURACRON
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Aegis.Active")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Aegis.Active")));
    }

    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Ruin.Active")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Ruin.Active")));
    }

    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.Vesper.Active")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.Vesper.Active")));
    }

    // Tags de ambientes para replicação otimizada
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Environment.Transition")))
    {
        HighPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Environment.Transition")));
    }

    // Tags de baixa prioridade (efeitos visuais)
    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Sigil.VFX")))
    {
        LowPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Sigil.VFX")));
    }

    if (UGameplayTagsManager::Get().IsValidGameplayTagString(TEXT("Environment.VFX")))
    {
        LowPriorityTags.AddTag(FGameplayTag::RequestGameplayTag(TEXT("Environment.VFX")));
    }

    UE_LOG(LogAURACRONNetwork, Log, TEXT("Tags de rede AURACRON configuradas: %d alta prioridade, %d críticas, %d baixa prioridade"),
        HighPriorityTags.Num(), CriticalTags.Num(), LowPriorityTags.Num());
}

void USigilNetworkConfig::InitializeNetworkMetrics()
{
    // Inicializar sistema de métricas de rede em tempo real
    CurrentNetworkStats = FSigilNetworkStats();

    // Configurar coleta de métricas
    NetworkMetricsConfig.bCollectBandwidthMetrics = true;
    NetworkMetricsConfig.bCollectLatencyMetrics = true;
    NetworkMetricsConfig.bCollectPacketLossMetrics = true;
    NetworkMetricsConfig.bCollectReplicationMetrics = true;
    NetworkMetricsConfig.MetricsCollectionInterval = 1.0f;
    NetworkMetricsConfig.MetricsHistorySize = 300; // 5 minutos a 1Hz

    // Inicializar arrays de histórico
    BandwidthHistory.Reserve(NetworkMetricsConfig.MetricsHistorySize);
    LatencyHistory.Reserve(NetworkMetricsConfig.MetricsHistorySize);
    PacketLossHistory.Reserve(NetworkMetricsConfig.MetricsHistorySize);
    ReplicationHistory.Reserve(NetworkMetricsConfig.MetricsHistorySize);

    UE_LOG(LogAURACRONNetwork, Log, TEXT("Sistema de métricas de rede inicializado"));
}

void USigilNetworkConfig::InitializeNetworkTimers()
{
    // Inicializar timers para sistemas automáticos
    if (UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull))
    {
        // Timer para otimização automática
        World->GetTimerManager().SetTimer(
            OptimizationTimerHandle,
            this,
            &USigilNetworkConfig::PerformAutomaticOptimization,
            FrequencySettings.OptimizationInterval,
            true
        );

        // Timer para coleta de métricas
        World->GetTimerManager().SetTimer(
            MetricsTimerHandle,
            this,
            &USigilNetworkConfig::CollectNetworkMetrics,
            NetworkMetricsConfig.MetricsCollectionInterval,
            true
        );

        // Timer para validação anti-cheat
        if (bEnableAntiCheatValidation)
        {
            World->GetTimerManager().SetTimer(
                AntiCheatTimerHandle,
                this,
                &USigilNetworkConfig::PerformAntiCheatValidation,
                1.0f / AntiCheatSettings.ValidationFrequency,
                true
            );
        }

        // Timer para telemetria
        if (bEnableNetworkTelemetry)
        {
            World->GetTimerManager().SetTimer(
                TelemetryTimerHandle,
                this,
                &USigilNetworkConfig::SendNetworkTelemetry,
                TelemetrySettings.TelemetryUpdateFrequency,
                true
            );
        }

        UE_LOG(LogAURACRONNetwork, Log, TEXT("Timers de rede inicializados"));
    }
}

void USigilNetworkConfig::PerformAutomaticOptimization()
{
    SCOPE_CYCLE_COUNTER(STAT_NetworkOptimization);

    // Otimização automática baseada nas métricas atuais
    if (!bEnableNetworkOptimizations)
    {
        return;
    }

    // Verificar se precisa ajustar configurações baseado na performance
    const float CurrentBandwidth = CurrentNetworkStats.CurrentBandwidthUsage;
    const float CurrentLatency = CurrentNetworkStats.AverageLatency;
    const float CurrentPacketLoss = CurrentNetworkStats.PacketLossPercentage;

    bool bNeedsOptimization = false;

    // Verificar se bandwidth está muito alto
    if (CurrentBandwidth > BandwidthSettings.MaxBandwidthPerPlayer * MaxPlayers * 0.8f)
    {
        // Reduzir frequência de replicação para dados não críticos
        FrequencySettings.LowPriorityFrequency = FMath::Max(FrequencySettings.LowPriorityFrequency * 0.9f, 1.0f);
        bNeedsOptimization = true;

        UE_LOG(LogAURACRONNetwork, Warning, TEXT("Bandwidth alto detectado (%f KB/s), reduzindo frequência de baixa prioridade para %f Hz"),
            CurrentBandwidth, FrequencySettings.LowPriorityFrequency);
    }

    // Verificar se latência está muito alta
    if (CurrentLatency > 100.0f) // 100ms
    {
        // Aumentar compressão e reduzir dados não essenciais
        BandwidthSettings.bUseDeltaCompression = true;
        bReduceEnemyDetails = true;
        bNeedsOptimization = true;

        UE_LOG(LogAURACRONNetwork, Warning, TEXT("Latência alta detectada (%f ms), habilitando otimizações"), CurrentLatency);
    }

    // Verificar packet loss
    if (CurrentPacketLoss > 2.0f) // 2%
    {
        // Reduzir tamanho de pacotes e aumentar redundância
        BandwidthSettings.MaxPacketSize = FMath::Max(BandwidthSettings.MaxPacketSize - 100, 512);
        bNeedsOptimization = true;

        UE_LOG(LogAURACRONNetwork, Warning, TEXT("Packet loss alto detectado (%f%%), reduzindo tamanho de pacote para %d bytes"),
            CurrentPacketLoss, BandwidthSettings.MaxPacketSize);
    }

    if (bNeedsOptimization)
    {
        // Broadcast evento de otimização
        OnNetworkOptimizationChanged.Broadcast(OptimizationType);
    }
}

void USigilNetworkConfig::CollectNetworkMetrics()
{
    // Coletar métricas de rede em tempo real
    if (!bEnableNetworkMetrics)
    {
        return;
    }

    // Obter world para acessar net driver
    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World || !World->GetNetDriver())
    {
        return;
    }

    UNetDriver* NetDriver = World->GetNetDriver();

    // Coletar estatísticas de bandwidth
    float TotalBandwidth = 0.0f;
    int32 ActiveConnections = 0;
    float TotalLatency = 0.0f;
    float TotalPacketLoss = 0.0f;
    int32 TotalReplications = 0;

    // Iterar por todas as conexões ativas
    for (UNetConnection* Connection : NetDriver->ClientConnections)
    {
        if (Connection && Connection->GetConnectionState() == USOCK_Open)
        {
            ActiveConnections++;

            // Coletar bandwidth
            TotalBandwidth += Connection->OutBytesPerSecond / 1024.0f; // Converter para KB/s

            // Coletar latência
            TotalLatency += Connection->AvgLag * 1000.0f; // Converter para ms

            // Coletar packet loss
            if (Connection->OutPacketsLost > 0 && Connection->OutPackets > 0)
            {
                TotalPacketLoss += (float)Connection->OutPacketsLost / (float)Connection->OutPackets * 100.0f;
            }

            // Coletar replicações
            TotalReplications += Connection->OutReliablePackets + Connection->OutUnreliablePackets;
        }
    }

    // Calcular médias
    if (ActiveConnections > 0)
    {
        CurrentNetworkStats.CurrentBandwidthUsage = TotalBandwidth;
        CurrentNetworkStats.AverageLatency = TotalLatency / ActiveConnections;
        CurrentNetworkStats.PacketLossPercentage = TotalPacketLoss / ActiveConnections;
        CurrentNetworkStats.ActiveConnections = ActiveConnections;
        CurrentNetworkStats.ReplicationsPerSecond = TotalReplications;
    }

    // Adicionar ao histórico
    BandwidthHistory.Add(CurrentNetworkStats.CurrentBandwidthUsage);
    LatencyHistory.Add(CurrentNetworkStats.AverageLatency);
    PacketLossHistory.Add(CurrentNetworkStats.PacketLossPercentage);
    ReplicationHistory.Add(CurrentNetworkStats.ReplicationsPerSecond);

    // Manter tamanho do histórico
    if (BandwidthHistory.Num() > NetworkMetricsConfig.MetricsHistorySize)
    {
        BandwidthHistory.RemoveAt(0);
        LatencyHistory.RemoveAt(0);
        PacketLossHistory.RemoveAt(0);
        ReplicationHistory.RemoveAt(0);
    }

    // Atualizar stats para profiling
    SET_FLOAT_STAT(STAT_BandwidthUsage, CurrentNetworkStats.CurrentBandwidthUsage);
    SET_DWORD_STAT(STAT_ReplicationsPerSecond, CurrentNetworkStats.ReplicationsPerSecond);

    // Broadcast evento de atualização de stats
    OnNetworkStatsUpdated.Broadcast(CurrentNetworkStats);

    // Log detalhado se habilitado
    if (bEnableDebugLogging)
    {
        UE_LOG(LogAURACRONNetwork, VeryVerbose, TEXT("Métricas de rede: Bandwidth=%f KB/s, Latência=%f ms, PacketLoss=%f%%, Conexões=%d, Replicações=%d"),
            CurrentNetworkStats.CurrentBandwidthUsage, CurrentNetworkStats.AverageLatency,
            CurrentNetworkStats.PacketLossPercentage, CurrentNetworkStats.ActiveConnections,
            CurrentNetworkStats.ReplicationsPerSecond);
    }
}

void USigilNetworkConfig::PerformAntiCheatValidation()
{
    SCOPE_CYCLE_COUNTER(STAT_AntiCheatValidation);

    // Validação anti-cheat server-side
    if (!bEnableAntiCheatValidation)
    {
        return;
    }

    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return;
    }

    // Validar todos os jogadores conectados
    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        // Validar velocidade de movimento
        if (AntiCheatSettings.bValidateMovement)
        {
            ValidatePlayerMovement(PC);
        }

        // Validar habilidades
        if (AntiCheatSettings.bValidateAbilities)
        {
            ValidatePlayerAbilities(PC);
        }

        // Validar recursos
        if (AntiCheatSettings.bValidateResources)
        {
            ValidatePlayerResources(PC);
        }
    }
}

void USigilNetworkConfig::ValidatePlayerMovement(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPawn())
    {
        return;
    }

    APawn* Pawn = PlayerController->GetPawn();
    FVector CurrentLocation = Pawn->GetActorLocation();

    // Verificar se há dados anteriores para este jogador
    FPlayerValidationData* ValidationData = PlayerValidationMap.Find(PlayerController);
    if (!ValidationData)
    {
        // Primeira validação, apenas armazenar dados
        FPlayerValidationData NewData;
        NewData.LastLocation = CurrentLocation;
        NewData.LastValidationTime = GetWorld()->GetTimeSeconds();
        NewData.SuspiciousActionCount = 0;
        PlayerValidationMap.Add(PlayerController, NewData);
        return;
    }

    const float CurrentTime = GetWorld()->GetTimeSeconds();
    const float DeltaTime = CurrentTime - ValidationData->LastValidationTime;

    if (DeltaTime > 0.0f)
    {
        // Calcular velocidade
        const float Distance = FVector::Dist(CurrentLocation, ValidationData->LastLocation);
        const float Speed = Distance / DeltaTime;

        // Obter velocidade máxima esperada (baseado no personagem)
        float MaxExpectedSpeed = 600.0f; // Velocidade base padrão
        if (UCharacterMovementComponent* MovementComp = Pawn->FindComponentByClass<UCharacterMovementComponent>())
        {
            MaxExpectedSpeed = MovementComp->MaxWalkSpeed;
        }

        // Aplicar tolerância
        MaxExpectedSpeed *= AntiCheatSettings.MaxMovementSpeedTolerance;

        // Verificar se velocidade é suspeita
        if (Speed > MaxExpectedSpeed)
        {
            ValidationData->SuspiciousActionCount++;

            UE_LOG(LogAURACRONNetwork, Warning, TEXT("ANTI-CHEAT: Velocidade suspeita detectada para jogador %s: %f (máx: %f)"),
                *PlayerController->GetName(), Speed, MaxExpectedSpeed);

            // Se muitas ações suspeitas, tomar ação
            if (ValidationData->SuspiciousActionCount >= AntiCheatSettings.SuspiciousActionThreshold)
            {
                HandleSuspiciousPlayer(PlayerController, TEXT("Speed Hack Detected"));
            }
        }
        else
        {
            // Reduzir contador de ações suspeitas se comportamento normal
            ValidationData->SuspiciousActionCount = FMath::Max(0, ValidationData->SuspiciousActionCount - 1);
        }

        // Atualizar dados de validação
        ValidationData->LastLocation = CurrentLocation;
        ValidationData->LastValidationTime = CurrentTime;
    }
}

void USigilNetworkConfig::ValidatePlayerAbilities(APlayerController* PlayerController)
{
    // Validação de habilidades (implementação específica do AURACRON)
    if (!PlayerController || !PlayerController->GetPawn())
    {
        return;
    }

    // Verificar se o jogador tem componente de habilidades
    if (UAbilitySystemComponent* ASC = PlayerController->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Validar cooldowns de habilidades
        TArray<FGameplayAbilitySpec> AbilitySpecs = ASC->GetActivatableAbilities();

        for (const FGameplayAbilitySpec& Spec : AbilitySpecs)
        {
            if (Spec.Ability)
            {
                // Verificar se habilidade está em cooldown no servidor
                float CooldownTimeRemaining = ASC->GetCooldownTimeRemaining(Spec.Ability->GetCooldownGameplayEffect());

                // Se habilidade foi usada recentemente mas não está em cooldown, é suspeito
                if (CooldownTimeRemaining <= 0.0f)
                {
                    // Verificar histórico de uso da habilidade
                    FPlayerValidationData* ValidationData = PlayerValidationMap.Find(PlayerController);
                    if (ValidationData)
                    {
                        // Lógica de validação específica para habilidades
                        // (implementação dependeria do sistema de habilidades específico)
                    }
                }
            }
        }
    }
}

void USigilNetworkConfig::ValidatePlayerResources(APlayerController* PlayerController)
{
    // Validação de recursos (vida, mana, etc.)
    if (!PlayerController || !PlayerController->GetPawn())
    {
        return;
    }

    // Verificar se o jogador tem AttributeSet
    if (UAbilitySystemComponent* ASC = PlayerController->GetPawn()->FindComponentByClass<UAbilitySystemComponent>())
    {
        // Validar se recursos não excedem limites máximos
        // (implementação específica dependeria do AttributeSet do AURACRON)

        FPlayerValidationData* ValidationData = PlayerValidationMap.Find(PlayerController);
        if (ValidationData)
        {
            // Verificar mudanças súbitas em recursos
            // Se recursos aumentaram muito rapidamente sem fonte válida, é suspeito
        }
    }
}

void USigilNetworkConfig::HandleSuspiciousPlayer(APlayerController* PlayerController, const FString& Reason)
{
    if (!PlayerController)
    {
        return;
    }

    UE_LOG(LogAURACRONNetwork, Error, TEXT("ANTI-CHEAT: Jogador suspeito detectado - %s: %s"),
        *PlayerController->GetName(), *Reason);

    // Registrar evento para telemetria
    FAntiCheatEvent AntiCheatEvent;
    AntiCheatEvent.PlayerName = PlayerController->GetName();
    AntiCheatEvent.Reason = Reason;
    AntiCheatEvent.Timestamp = FDateTime::Now();
    AntiCheatEvent.Severity = EAntiCheatSeverity::High;

    AntiCheatEvents.Add(AntiCheatEvent);

    // Integração com EOS Anti-Cheat (se disponível)
    if (bEnableEOSIntegration)
    {
        ReportToEOSAntiCheat(PlayerController, Reason);
    }

    // Ações baseadas na severidade
    // Por enquanto apenas log, mas poderia incluir kick/ban
}

void USigilNetworkConfig::ReportToEOSAntiCheat(APlayerController* PlayerController, const FString& Reason)
{
    // Integração com Epic Online Services Anti-Cheat
    if (IOnlineSubsystem* OnlineSubsystem = IOnlineSubsystem::Get())
    {
        // Implementação específica para EOS Anti-Cheat
        // Requer configuração adicional do EOS SDK

        UE_LOG(LogAURACRONNetwork, Log, TEXT("Reportando jogador suspeito para EOS Anti-Cheat: %s - %s"),
            *PlayerController->GetName(), *Reason);
    }
}

void USigilNetworkConfig::SendNetworkTelemetry()
{
    // Enviar dados de telemetria para análise
    if (!bEnableNetworkTelemetry)
    {
        return;
    }

    // Preparar dados de telemetria
    FNetworkTelemetryData TelemetryData;
    TelemetryData.Timestamp = FDateTime::Now();
    TelemetryData.NetworkStats = CurrentNetworkStats;
    TelemetryData.OptimizationType = OptimizationType;
    TelemetryData.MaxPlayers = MaxPlayers;
    TelemetryData.ActiveConnections = CurrentNetworkStats.ActiveConnections;

    // Calcular médias do histórico
    if (BandwidthHistory.Num() > 0)
    {
        float TotalBandwidth = 0.0f;
        for (float Bandwidth : BandwidthHistory)
        {
            TotalBandwidth += Bandwidth;
        }
        TelemetryData.AverageBandwidth = TotalBandwidth / BandwidthHistory.Num();
    }

    if (LatencyHistory.Num() > 0)
    {
        float TotalLatency = 0.0f;
        for (float Latency : LatencyHistory)
        {
            TotalLatency += Latency;
        }
        TelemetryData.AverageLatency = TotalLatency / LatencyHistory.Num();
    }

    // Adicionar eventos de anti-cheat
    TelemetryData.AntiCheatEvents = AntiCheatEvents;

    // Enviar para sistema de telemetria
    SendTelemetryToBackend(TelemetryData);

    // Limpar eventos processados
    AntiCheatEvents.Empty();

    UE_LOG(LogAURACRONNetwork, VeryVerbose, TEXT("Telemetria de rede enviada: Bandwidth=%f KB/s, Latência=%f ms, Eventos Anti-Cheat=%d"),
        TelemetryData.AverageBandwidth, TelemetryData.AverageLatency, TelemetryData.AntiCheatEvents.Num());
}

void USigilNetworkConfig::SendTelemetryToBackend(const FNetworkTelemetryData& TelemetryData)
{
    // Implementação para enviar dados para backend
    // Poderia usar HTTP requests, Firebase, ou outro sistema de telemetria

    // Por enquanto, apenas log local
    if (bEnableDebugLogging)
    {
        UE_LOG(LogAURACRONNetwork, Log, TEXT("Telemetria preparada para envio: %d conexões ativas, %f KB/s bandwidth médio"),
            TelemetryData.ActiveConnections, TelemetryData.AverageBandwidth);
    }
}

// Implementações das funções de interesse management e predição

void USigilNetworkConfig::UpdateInterestManagement()
{
    SCOPE_CYCLE_COUNTER(STAT_InterestManagement);

    if (!bEnableInterestManagement)
    {
        return;
    }

    UWorld* World = GEngine->GetWorldFromContextObject(this, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        return;
    }

    // Implementar interest management baseado em relevância espacial
    // Atualizar quais atores são relevantes para cada jogador

    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (!PC || !PC->GetPawn())
        {
            continue;
        }

        UpdatePlayerInterestManagement(PC);
    }
}

void USigilNetworkConfig::UpdatePlayerInterestManagement(APlayerController* PlayerController)
{
    if (!PlayerController || !PlayerController->GetPawn())
    {
        return;
    }

    FVector PlayerLocation = PlayerController->GetPawn()->GetActorLocation();

    // Implementar lógica de relevância baseada em:
    // 1. Distância
    // 2. Linha de visão
    // 3. Prioridade de gameplay (objetivos, companheiros de equipe)
    // 4. Tags específicas do AURACRON (Sígilos, ambientes)

    // Esta implementação seria específica para o sistema de replicação do UE
}